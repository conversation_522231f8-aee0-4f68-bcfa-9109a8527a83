#!/bin/bash


# Script to enable common development features in Suna
# This script enables feature flags that are commonly needed in development


echo"🚀 Enabling development features for Suna..."


# Check if Docker is available
if!command-vdocker &> /dev/null; then
echo"❌ Docker not found. Please make sure Docker is installed and running."
exit1
fi


# Check if Redis container is running
if!dockerps|grep-q"cfox-redis-1"; then
echo"❌ Redis container (cfox-redis-1) is not running. Please start your Docker containers first."
exit1
fi


echo"📡 Connecting to Redis..."


# Enable custom_agents feature flag
echo"🤖 Enabling custom agents feature..."
dockerexeccfox-redis-1redis-cliHSETfeature_flag:custom_agentsenabledtruedescription"Enable custom agents feature for dev environment"updated_at"$(date-u +%Y-%m-%dT%H:%M:%SZ)">/dev/null
dockerexeccfox-redis-1redis-cliSADDfeature_flagscustom_agents>/dev/null


# Enable agent_marketplace feature flag (if needed)
echo"🏪 Enabling agent marketplace feature..."
dockerexeccfox-redis-1redis-cliHSETfeature_flag:agent_marketplaceenabledtruedescription"Enable agent marketplace for dev environment"updated_at"$(date-u +%Y-%m-%dT%H:%M:%SZ)">/dev/null
dockerexeccfox-redis-1redis-cliSADDfeature_flagsagent_marketplace>/dev/null


# Verify the flags are enabled
echo"✅ Verifying feature flags..."
CUSTOM_AGENTS_STATUS=$(curl-shttp://localhost:8000/api/feature-flags/custom_agents|grep-o'"enabled":[^,]*'|cut-d':'-f2)
if [[ "$CUSTOM_AGENTS_STATUS"=="true" ]]; then
echo"✅ Custom agents feature: ENABLED"
else
echo"❌ Custom agents feature: FAILED"
fi


echo""
echo"🎉 Development features have been enabled!"
echo""
echo"📋 What's now available:"
echo" • Agents menu in sidebar"
echo" • Integrations menu in sidebar"
echo" • Agent marketplace"
echo" • Custom agent creation"
echo""
echo"🔄 Please refresh your browser to see the changes."
echo""
echo"💡 To disable these features later, run:"
echo" docker exec cfox-redis-1 redis-cli HSET feature_flag:custom_agents enabled false"

